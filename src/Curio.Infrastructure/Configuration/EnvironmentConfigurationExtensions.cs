using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Curio.Infrastructure.Configuration;

/// <summary>
/// 环境配置扩展方法，支持灵活的本地开发配置
/// </summary>
public static class EnvironmentConfigurationExtensions
{
    /// <summary>
    /// 添加环境特定的配置源，支持本地开发的灵活配置
    /// </summary>
    public static IConfigurationBuilder AddFlexibleEnvironmentConfiguration(
        this IConfigurationBuilder builder, 
        IHostEnvironment environment)
    {
        // 1. 基础配置文件（版本控制）
        builder.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        builder.AddJsonFile($"appsettings.{environment.EnvironmentName}.json", optional: true, reloadOnChange: true);

        // 2. 本地开发配置支持
        if (environment.IsDevelopment())
        {
            // 用户机密（推荐用于敏感信息）
            builder.AddUserSecrets<Program>();

            // .env 文件支持（按优先级加载）
            LoadEnvironmentFiles(builder);
        }

        // 3. 环境变量（最高优先级）
        builder.AddEnvironmentVariables();

        return builder;
    }

    /// <summary>
    /// 按优先级加载环境变量文件
    /// </summary>
    private static void LoadEnvironmentFiles(IConfigurationBuilder builder)
    {
        var currentDirectory = Directory.GetCurrentDirectory();
        
        // 按优先级顺序加载（后加载的会覆盖先加载的）
        var envFiles = new[]
        {
            ".env",                    // 基础环境变量（可提交到版本控制）
            ".env.development",        // 开发环境特定（可提交到版本控制）
            ".env.local",             // 本地覆盖（不提交到版本控制）
            ".env.development.local"   // 本地开发覆盖（不提交到版本控制）
        };

        foreach (var envFile in envFiles)
        {
            var filePath = Path.Combine(currentDirectory, envFile);
            if (File.Exists(filePath))
            {
                builder.AddEnvironmentVariablesFromFile(filePath);
            }
        }
    }

    /// <summary>
    /// 从文件加载环境变量
    /// </summary>
    private static IConfigurationBuilder AddEnvironmentVariablesFromFile(
        this IConfigurationBuilder builder, 
        string filePath)
    {
        var envVars = new Dictionary<string, string?>();

        foreach (var line in File.ReadAllLines(filePath))
        {
            var trimmedLine = line.Trim();
            
            // 跳过空行和注释
            if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith('#'))
                continue;

            var separatorIndex = trimmedLine.IndexOf('=');
            if (separatorIndex <= 0) continue;

            var key = trimmedLine[..separatorIndex].Trim();
            var value = trimmedLine[(separatorIndex + 1)..].Trim();

            // 移除引号
            if ((value.StartsWith('"') && value.EndsWith('"')) ||
                (value.StartsWith('\'') && value.EndsWith('\'')))
            {
                value = value[1..^1];
            }

            envVars[key] = value;
        }

        builder.AddInMemoryCollection(envVars);
        return builder;
    }

    /// <summary>
    /// 获取当前配置的摘要信息（用于调试）
    /// </summary>
    public static string GetConfigurationSummary(this IConfiguration configuration)
    {
        var summary = new List<string>
        {
            "=== Configuration Summary ===",
            $"Environment: {configuration["ASPNETCORE_ENVIRONMENT"]}",
            "",
            "Database:",
            $"  Host: {configuration["DATABASE_HOST"]}",
            $"  Port: {configuration["DATABASE_PORT"]}",
            $"  Database: {configuration["DATABASE_NAME"]}",
            $"  Username: {configuration["DATABASE_USERNAME"]}",
            $"  Password: {(string.IsNullOrEmpty(configuration["DATABASE_PASSWORD"]) ? "Not Set" : "***")}",
            "",
            "Kafka:",
            $"  Brokers: {configuration["KAFKA_BROKERS"]}",
            $"  Consumer Group: {configuration["KAFKA_CONSUMER_GROUP_ID"]}",
            $"  Security Protocol: {configuration["KAFKA_SECURITY_PROTOCOL"]}",
            "",
            "Orleans:",
            $"  Cluster ID: {configuration["ORLEANS_CLUSTER_ID"]}",
            $"  Service ID: {configuration["ORLEANS_SERVICE_ID"]}",
            "",
            "Security:",
            $"  JWT Secret: {(string.IsNullOrEmpty(configuration["JWT_SECRET_KEY"]) ? "Not Set" : "***")}",
            $"  Encryption Key: {(string.IsNullOrEmpty(configuration["ENCRYPTION_KEY"]) ? "Not Set" : "***")}",
            ""
        };

        return string.Join(Environment.NewLine, summary);
    }

    /// <summary>
    /// 检查配置是否来自环境变量
    /// </summary>
    public static bool IsFromEnvironmentVariable(this IConfiguration configuration, string key)
    {
        // 检查环境变量中是否存在该键
        return !string.IsNullOrEmpty(Environment.GetEnvironmentVariable(key));
    }

    /// <summary>
    /// 获取配置值的来源信息
    /// </summary>
    public static string GetConfigurationSource(this IConfiguration configuration, string key)
    {
        var value = configuration[key];
        if (string.IsNullOrEmpty(value))
            return "Not Set";

        if (configuration.IsFromEnvironmentVariable(key))
            return "Environment Variable";

        // 这里可以扩展检查其他来源
        return "Configuration File";
    }
}

/// <summary>
/// 配置调试助手
/// </summary>
public static class ConfigurationDebugHelper
{
    /// <summary>
    /// 打印配置摘要（仅在开发环境）
    /// </summary>
    public static void PrintConfigurationSummary(IConfiguration configuration, IHostEnvironment environment)
    {
        if (!environment.IsDevelopment()) return;

        Console.WriteLine();
        Console.WriteLine(configuration.GetConfigurationSummary());
        Console.WriteLine("=== End Configuration Summary ===");
        Console.WriteLine();
    }

    /// <summary>
    /// 检查关键配置项
    /// </summary>
    public static void ValidateKeyConfigurations(IConfiguration configuration)
    {
        var keyConfigs = new Dictionary<string, string>
        {
            ["Database Host"] = configuration["DATABASE_HOST"] ?? "localhost",
            ["Database Port"] = configuration["DATABASE_PORT"] ?? "5432",
            ["Kafka Brokers"] = configuration["KAFKA_BROKERS"] ?? "localhost:9092",
            ["Orleans Cluster"] = configuration["ORLEANS_CLUSTER_ID"] ?? "curio-cluster-dev"
        };

        Console.WriteLine("🔍 Key Configuration Check:");
        foreach (var config in keyConfigs)
        {
            var source = configuration.GetConfigurationSource(config.Key.Replace(" ", "_").ToUpper());
            Console.WriteLine($"  {config.Key}: {config.Value} (Source: {source})");
        }
        Console.WriteLine();
    }
}
