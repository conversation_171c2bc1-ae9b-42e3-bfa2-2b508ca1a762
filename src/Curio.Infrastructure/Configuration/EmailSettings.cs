namespace Curio.Infrastructure.Configuration;

/// <summary>
/// 数据库配置设置
/// </summary>
public class DatabaseSettings
{
    public const string SectionName = "Database";

    /// <summary>
    /// 完整的数据库连接字符串（优先使用）
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 数据库主机地址
    /// </summary>
    public string Host { get; set; } = "localhost";

    /// <summary>
    /// 数据库端口
    /// </summary>
    public int Port { get; set; } = 5432;

    /// <summary>
    /// 数据库名称
    /// </summary>
    public string Database { get; set; } = "curio";

    /// <summary>
    /// 数据库用户名
    /// </summary>
    public string Username { get; set; } = "curio";

    /// <summary>
    /// 数据库密码
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 命令超时时间（秒）
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用敏感数据日志记录
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// 构建连接字符串
    /// </summary>
    public string GetConnectionString()
    {
        if (!string.IsNullOrEmpty(ConnectionString))
        {
            return ConnectionString;
        }

        return $"Host={Host};Port={Port};Database={Database};Username={Username};Password={Password}";
    }
}

/// <summary>
/// Kafka 消息队列配置设置
/// </summary>
public class KafkaSettings
{
    public const string SectionName = "Kafka";

    /// <summary>
    /// Kafka 代理服务器列表
    /// </summary>
    public string[] BrokerList { get; set; } = ["localhost:9092"];

    /// <summary>
    /// 消费者组标识符
    /// </summary>
    public string ConsumerGroupId { get; set; } = "orleans-event-streams";

    /// <summary>
    /// 主题列表
    /// </summary>
    public string[] Topics { get; set; } = ["domain-events", "verification-events", "user-events", "email-events"];

    /// <summary>
    /// 会话超时时间（毫秒）
    /// </summary>
    public int SessionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// 心跳间隔时间（毫秒）
    /// </summary>
    public int HeartbeatIntervalMs { get; set; } = 3000;

    /// <summary>
    /// 自动偏移重置策略
    /// </summary>
    public string AutoOffsetReset { get; set; } = "earliest";

    /// <summary>
    /// 是否启用自动提交
    /// </summary>
    public bool EnableAutoCommit { get; set; } = true;

    /// <summary>
    /// 自动提交间隔时间（毫秒）
    /// </summary>
    public int AutoCommitIntervalMs { get; set; } = 5000;

    /// <summary>
    /// 最大轮询记录数
    /// </summary>
    public int MaxPollRecords { get; set; } = 500;

    /// <summary>
    /// 最小获取字节数
    /// </summary>
    public int FetchMinBytes { get; set; } = 1;

    /// <summary>
    /// 最大获取等待时间（毫秒）
    /// </summary>
    public int FetchMaxWaitMs { get; set; } = 500;

    /// <summary>
    /// 安全协议
    /// </summary>
    public string SecurityProtocol { get; set; } = "PLAINTEXT"; // PLAINTEXT, SASL_PLAINTEXT

    /// <summary>
    /// SASL 认证机制
    /// </summary>
    public string SaslMechanism { get; set; } = "PLAIN";

    /// <summary>
    /// SASL 用户名
    /// </summary>
    public string SaslUsername { get; set; } = string.Empty;

    /// <summary>
    /// SASL 密码
    /// </summary>
    public string SaslPassword { get; set; } = string.Empty;
}

/// <summary>
/// Orleans 集群配置设置
/// </summary>
public class OrleansSettings
{
    public const string SectionName = "Orleans";

    /// <summary>
    /// 集群标识符
    /// </summary>
    public string ClusterId { get; set; } = "curio-cluster";

    /// <summary>
    /// 服务标识符
    /// </summary>
    public string ServiceId { get; set; } = "curio-service";

    /// <summary>
    /// 集群配置
    /// </summary>
    public ClusteringSettings Clustering { get; set; } = new();

    /// <summary>
    /// 存储配置
    /// </summary>
    public StorageSettings Storage { get; set; } = new();

    /// <summary>
    /// 流处理配置
    /// </summary>
    public StreamingSettings Streaming { get; set; } = new();

    /// <summary>
    /// 提醒服务配置
    /// </summary>
    public ReminderSettings Reminders { get; set; } = new();
}

/// <summary>
/// Orleans 集群配置
/// </summary>
public class ClusteringSettings
{
    /// <summary>
    /// 集群提供程序类型
    /// </summary>
    public string Provider { get; set; } = "AdoNet"; // AdoNet, Consul, Memory

    /// <summary>
    /// 集群连接字符串（如果为空则使用数据库连接字符串）
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 刷新周期（秒）
    /// </summary>
    public int RefreshPeriod { get; set; } = 30;

    /// <summary>
    /// 死亡投票过期超时（秒）
    /// </summary>
    public int DeathVoteExpirationTimeout { get; set; } = 120;
}

/// <summary>
/// Orleans 存储配置
/// </summary>
public class StorageSettings
{
    /// <summary>
    /// 默认存储提供程序
    /// </summary>
    public string DefaultProvider { get; set; } = "AdoNet";

    /// <summary>
    /// 存储连接字符串（如果为空则使用数据库连接字符串）
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 是否使用 JSON 格式存储
    /// </summary>
    public bool UseJsonFormat { get; set; } = true;
}

/// <summary>
/// Orleans 流处理配置
/// </summary>
public class StreamingSettings
{
    /// <summary>
    /// 流处理提供程序
    /// </summary>
    public string Provider { get; set; } = "Kafka";

    /// <summary>
    /// 流处理连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;
}

/// <summary>
/// Orleans 提醒服务配置
/// </summary>
public class ReminderSettings
{
    /// <summary>
    /// 提醒服务提供程序
    /// </summary>
    public string Provider { get; set; } = "AdoNet";

    /// <summary>
    /// 提醒服务连接字符串（如果为空则使用数据库连接字符串）
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;
}

// Application Configuration
public class ApplicationSettings
{
    public const string SectionName = "Application";

    public string Name { get; set; } = "Curio API";
    public string Version { get; set; } = "1.0.0";
    public string Environment { get; set; } = "Development";
    public ApiSettings Api { get; set; } = new();
    public SecuritySettings Security { get; set; } = new();
}

/// <summary>
/// API 服务配置
/// </summary>
public class ApiSettings
{
    /// <summary>
    /// API 基础 URL
    /// </summary>
    public string BaseUrl { get; set; } = "https://localhost:7274";

    /// <summary>
    /// 允许的主机列表
    /// </summary>
    public string[] AllowedHosts { get; set; } = ["*"];

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 最大请求体大小（字节）
    /// </summary>
    public int MaxRequestBodySize { get; set; } = 10485760; // 10MB

    /// <summary>
    /// CORS 配置
    /// </summary>
    public CorsSettings Cors { get; set; } = new();
}

/// <summary>
/// CORS 跨域配置
/// </summary>
public class CorsSettings
{
    /// <summary>
    /// 是否启用 CORS
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 允许的源地址列表
    /// </summary>
    public string[] AllowedOrigins { get; set; } = ["*"];

    /// <summary>
    /// 允许的 HTTP 方法列表
    /// </summary>
    public string[] AllowedMethods { get; set; } = ["GET", "POST", "PUT", "DELETE", "OPTIONS"];

    /// <summary>
    /// 允许的请求头列表
    /// </summary>
    public string[] AllowedHeaders { get; set; } = ["*"];

    /// <summary>
    /// 是否允许凭据
    /// </summary>
    public bool AllowCredentials { get; set; } = true;
}

public class SecuritySettings
{
    public JwtSettings Jwt { get; set; } = new();
    public EncryptionSettings Encryption { get; set; } = new();
}

public class JwtSettings
{
    public string SecretKey { get; set; } = string.Empty;
    public string Issuer { get; set; } = "Curio";
    public string Audience { get; set; } = "Curio.Api";
    public int ExpirationMinutes { get; set; } = 60;
    public int RefreshTokenExpirationDays { get; set; } = 7;
}

public class EncryptionSettings
{
    public string Key { get; set; } = string.Empty;
    public string Salt { get; set; } = string.Empty;
}

// Email Configuration (existing)
public class EmailSettings
{
    public const string SectionName = "Email";

    public SmtpSettings Smtp { get; set; } = new();
    public TemplateSettings Templates { get; set; } = new();
    public SenderSettings DefaultSender { get; set; } = new();
    public RetrySettings Retry { get; set; } = new();
}

public class SmtpSettings
{
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; } = 587;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool EnableSsl { get; set; } = true;
    public int TimeoutSeconds { get; set; } = 30;
    public bool UseDefaultCredentials { get; set; } = false;
}

public class TemplateSettings
{
    public string TemplatesDirectory { get; set; } = "EmailTemplates";
    public bool EnableCaching { get; set; } = true;
    public int CacheExpirationMinutes { get; set; } = 60;
}

public class SenderSettings
{
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public string ReplyToEmail { get; set; } = string.Empty;
    public string ReplyToName { get; set; } = string.Empty;
}

public class RetrySettings
{
    public int MaxAttempts { get; set; } = 3;
    public int DelayMilliseconds { get; set; } = 1000;
    public bool ExponentialBackoff { get; set; } = true;
}