{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Orleans": "Information", "Microsoft.Orleans": "Warning", "Curio.Infrastructure.Services": "Debug"}}, "Application": {"Name": "Curio Orleans Silo", "Version": "1.0.0", "Environment": "Development"}, "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=curio;Password=********", "Host": "localhost", "Port": 5432, "Database": "curio", "Username": "curio", "Password": "********", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": true}, "Orleans": {"ClusterId": "curio-cluster-dev", "ServiceId": "curio-service-dev", "Clustering": {"Provider": "AdoNet", "ConnectionString": "", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": "localhost:9092"}, "Reminders": {"Provider": "AdoNet", "ConnectionString": ""}}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams-dev", "Topics": ["domain-events", "verification-events", "user-events", "dead-letter-queue", "projection-rebuild"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "PLAINTEXT"}, "Email": {"Smtp": {"Host": "", "Port": 587, "Username": "", "Password": "", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 5}, "DefaultSender": {"FromEmail": "", "FromName": "Curio Development", "ReplyToEmail": "", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}, "ResilientPublish": {"MaxRetries": 3, "BaseDelayMs": 1000, "BackupDirectory": "failed-events", "EnableDeadLetterQueue": true, "EnableLocalBackup": true, "PublishTimeoutSeconds": 10}}