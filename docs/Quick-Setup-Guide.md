# Curio API 快速设置指南

## 🚀 多人协作的灵活配置方案

### 📁 配置文件层次结构

```
配置优先级（从低到高）：
1. appsettings.json                 # 基础配置（版本控制）
2. appsettings.Development.json     # 开发环境配置（版本控制）
3. .env                            # 团队共享默认值（版本控制）
4. .env.development                # 开发环境默认值（版本控制）
5. .env.local                      # 个人本地配置（不提交）
6. .env.development.local          # 个人开发配置（不提交）
7. 环境变量                         # 系统环境变量（最高优先级）
```

## 🔧 快速开始

### 1. 克隆项目后的首次设置

```bash
# 1. 复制个人配置模板
cp .env.example .env.local

# 2. 编辑个人配置
nano .env.local  # 或使用你喜欢的编辑器
```

### 2. 个人配置示例 (.env.local)

```bash
# 个人数据库配置（如果你的 PostgreSQL 设置不同）
DATABASE_HOST=localhost
DATABASE_PORT=5433          # 如果你使用不同端口
DATABASE_PASSWORD=mypassword

# 个人 Kafka 配置（如果你的 Kafka 设置不同）
KAFKA_BROKERS=localhost:9093,localhost:9094  # 如果你有集群设置

# 个人邮件配置（如果需要测试邮件功能）
SMTP_HOST=smtp.qq.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM_ADDRESS=<EMAIL>

# 个人安全配置（可选，使用不同的密钥）
JWT_SECRET_KEY=my-personal-jwt-secret-key-for-local-development-32-chars
ENCRYPTION_KEY=my-personal-encryption-key-32-chars
```

## 🔄 常见场景

### 场景1：团队成员使用不同的数据库端口

**张三的 .env.local：**
```bash
DATABASE_PORT=5432
DATABASE_PASSWORD=zhang123
```

**李四的 .env.local：**
```bash
DATABASE_PORT=5433
DATABASE_PASSWORD=li456
```

### 场景2：某个成员需要连接远程 Kafka

**王五的 .env.local：**
```bash
KAFKA_BROKERS=remote-kafka:9092
KAFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
KAFKA_SASL_USERNAME=wangwu
KAFKA_SASL_PASSWORD=secret123
```

### 场景3：测试环境配置变更

**创建 .env.testing：**
```bash
DATABASE_HOST=test-db.company.com
KAFKA_BROKERS=test-kafka.company.com:9092
ORLEANS_CLUSTER_ID=curio-cluster-test
```

**运行测试：**
```bash
ASPNETCORE_ENVIRONMENT=Testing dotnet run
```

## 🛠️ 配置管理工具

### 查看当前配置

启动应用时，开发环境会自动显示配置摘要：

```
=== Configuration Summary ===
Environment: Development

Database:
  Host: localhost
  Port: 5432
  Database: curio
  Username: curio
  Password: ***

Kafka:
  Brokers: localhost:9092
  Consumer Group: orleans-event-streams-dev
  Security Protocol: PLAINTEXT

🔍 Key Configuration Check:
  Database Host: localhost (Source: Environment Variable)
  Database Port: 5432 (Source: Configuration File)
  Kafka Brokers: localhost:9092 (Source: Environment Variable)
  Orleans Cluster: curio-cluster-dev (Source: Configuration File)
```

### 配置调试命令

```bash
# 查看所有环境变量
printenv | grep -E "(DATABASE|KAFKA|ORLEANS|JWT)"

# 检查特定配置文件
cat .env.local

# 验证配置加载
dotnet run --environment Development
```

## 📋 团队协作最佳实践

### ✅ 推荐做法

1. **提交到版本控制的文件：**
   - `.env` - 团队共享的默认配置
   - `.env.development` - 开发环境特定配置
   - `appsettings.json` - 基础应用配置
   - `appsettings.Development.json` - 开发环境应用配置

2. **不提交到版本控制的文件：**
   - `.env.local` - 个人本地配置
   - `.env.*.local` - 个人环境特定配置
   - `appsettings.Production.json` - 生产环境配置

3. **配置变更流程：**
   ```bash
   # 1. 如果是团队共享的配置变更
   git add .env .env.development
   git commit -m "Update team default Kafka brokers"
   
   # 2. 如果是个人配置
   # 只修改 .env.local，不提交
   ```

### ❌ 避免的做法

1. **不要**在 `.env.local` 中设置团队通用的配置
2. **不要**提交包含个人敏感信息的配置文件
3. **不要**在配置文件中硬编码生产环境的敏感信息

## 🔧 故障排除

### 配置不生效？

1. **检查文件优先级：**
   ```bash
   # 环境变量 > .env.local > .env.development > .env > appsettings.json
   ```

2. **检查文件格式：**
   ```bash
   # 正确格式
   DATABASE_HOST=localhost
   
   # 错误格式
   DATABASE_HOST = localhost  # 不要有空格
   ```

3. **检查文件编码：**
   ```bash
   # 确保文件是 UTF-8 编码，没有 BOM
   file .env.local
   ```

### 配置冲突？

```bash
# 查看配置来源
dotnet run  # 查看启动时的配置摘要

# 临时禁用某个配置文件
mv .env.local .env.local.bak
dotnet run
```

### 敏感信息泄露？

```bash
# 检查是否意外提交了敏感文件
git status
git log --name-only | grep -E "\.env\.local|secrets"

# 如果意外提交，立即从历史中移除
git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch .env.local' --prune-empty --tag-name-filter cat -- --all
```

## 🚀 生产环境部署

生产环境不使用 `.env` 文件，而是使用：

1. **容器环境变量**
2. **Kubernetes Secrets**
3. **云服务密钥管理**

详见 [Production-Deployment-Guide.md](Production-Deployment-Guide.md)

---

这个配置方案让每个团队成员都能灵活地配置自己的本地环境，同时保持团队配置的一致性。
