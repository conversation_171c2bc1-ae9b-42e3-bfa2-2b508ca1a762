# Curio API Local Development Configuration
# Copy this file to .env.local and customize for your local environment
# .env.local is ignored by git, so each developer can have their own settings

# =============================================================================
# 🔧 LOCAL DEVELOPMENT SETTINGS (Customize these for your setup)
# =============================================================================

# Database Configuration (Customize for your local PostgreSQL)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=curio
DATABASE_USERNAME=curio
DATABASE_PASSWORD=curio123

# Kafka Configuration (Customize for your local Kafka)
KAFKA_BROKERS=localhost:9092
KAFKA_CONSUMER_GROUP_ID=orleans-event-streams-dev

# Email Configuration (Optional - only if you need to test email)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM_ADDRESS=
EMAIL_FROM_NAME=Curio Dev

# =============================================================================
# 🔐 SECURITY SETTINGS (Use different values for each environment)
# =============================================================================

# JWT Configuration
JWT_SECRET_KEY=dev-jwt-secret-key-change-this-for-your-local-environment-32-chars-min
JWT_ISSUER=Curio.Development
JWT_AUDIENCE=Curio.Api.Development

# Encryption Configuration
ENCRYPTION_KEY=dev-encryption-key-32-characters
ENCRYPTION_SALT=dev-salt-16-char

# =============================================================================
# 🌐 ENVIRONMENT SETTINGS (Usually don't need to change)
# =============================================================================

# Application Environment
ASPNETCORE_ENVIRONMENT=Development

# API Configuration
API_BASE_URL=https://localhost:7274
ALLOWED_HOSTS=*
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://localhost:7274

# Orleans Configuration
ORLEANS_CLUSTER_ID=curio-cluster-dev
ORLEANS_SERVICE_ID=curio-service-dev

# Kafka Security (for local development, usually PLAINTEXT)
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=PLAIN
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=

# Resilient Publish Configuration
RESILIENT_PUBLISH_MAX_RETRIES=3
RESILIENT_PUBLISH_BACKUP_DIR=failed-events
RESILIENT_PUBLISH_TIMEOUT=10
