#!/bin/bash

# Curio API - Kafka 配置更新脚本
# 用于快速更新团队的 Kafka 配置

set -e

echo "🔧 Curio API Kafka Configuration Updater"
echo "========================================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "Usage: $0 <kafka-brokers> [consumer-group-id] [security-protocol]"
    echo ""
    echo "Examples:"
    echo "  $0 localhost:9092"
    echo "  $0 kafka1:9092,kafka2:9092,kafka3:9092"
    echo "  $0 remote-kafka:9092 orleans-streams-prod SASL_PLAINTEXT"
    echo ""
    exit 1
fi

KAFKA_BROKERS="$1"
CONSUMER_GROUP_ID="${2:-orleans-event-streams-dev}"
SECURITY_PROTOCOL="${3:-PLAINTEXT}"

echo "📝 Configuration to apply:"
echo "  Kafka Brokers: $KAFKA_BROKERS"
echo "  Consumer Group: $CONSUMER_GROUP_ID"
echo "  Security Protocol: $SECURITY_PROTOCOL"
echo ""

# 询问更新范围
echo "🎯 Select update scope:"
echo "1) Update team defaults (.env)"
echo "2) Update my local config (.env.local)"
echo "3) Update both"
echo ""
read -p "Enter choice (1-3): " choice

update_team_config() {
    echo "📝 Updating team default configuration (.env)..."
    
    # 备份原文件
    if [ -f .env ]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        echo "✅ Backup created: .env.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 更新配置
    if [ -f .env ]; then
        sed -i.tmp "s/^KAFKA_BROKERS=.*/KAFKA_BROKERS=$KAFKA_BROKERS/" .env
        sed -i.tmp "s/^KAFKA_CONSUMER_GROUP_ID=.*/KAFKA_CONSUMER_GROUP_ID=$CONSUMER_GROUP_ID/" .env
        sed -i.tmp "s/^KAFKA_SECURITY_PROTOCOL=.*/KAFKA_SECURITY_PROTOCOL=$SECURITY_PROTOCOL/" .env
        rm .env.tmp
    else
        echo "⚠️  .env file not found, creating new one..."
        cat > .env << EOF
# Kafka Configuration
KAFKA_BROKERS=$KAFKA_BROKERS
KAFKA_CONSUMER_GROUP_ID=$CONSUMER_GROUP_ID
KAFKA_SECURITY_PROTOCOL=$SECURITY_PROTOCOL
EOF
    fi
    
    echo "✅ Team configuration updated"
}

update_local_config() {
    echo "📝 Updating local configuration (.env.local)..."
    
    # 创建或更新 .env.local
    if [ ! -f .env.local ]; then
        echo "# Personal Kafka Configuration" > .env.local
    fi
    
    # 移除旧的 Kafka 配置
    grep -v "^KAFKA_" .env.local > .env.local.tmp || true
    
    # 添加新的 Kafka 配置
    cat >> .env.local.tmp << EOF

# Kafka Configuration (Updated $(date))
KAFKA_BROKERS=$KAFKA_BROKERS
KAFKA_CONSUMER_GROUP_ID=$CONSUMER_GROUP_ID
KAFKA_SECURITY_PROTOCOL=$SECURITY_PROTOCOL
EOF
    
    mv .env.local.tmp .env.local
    echo "✅ Local configuration updated"
}

case $choice in
    1)
        update_team_config
        echo ""
        echo "🚨 Remember to commit the changes:"
        echo "   git add .env"
        echo "   git commit -m 'Update team Kafka configuration'"
        ;;
    2)
        update_local_config
        ;;
    3)
        update_team_config
        update_local_config
        echo ""
        echo "🚨 Remember to commit the team changes:"
        echo "   git add .env"
        echo "   git commit -m 'Update team Kafka configuration'"
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo ""
echo "🔍 Verifying configuration..."

# 显示当前配置
if [ -f .env ]; then
    echo "📄 Team configuration (.env):"
    grep "^KAFKA_" .env | sed 's/^/  /'
fi

if [ -f .env.local ]; then
    echo "📄 Local configuration (.env.local):"
    grep "^KAFKA_" .env.local | sed 's/^/  /' || echo "  (No Kafka config in .env.local)"
fi

echo ""
echo "✅ Configuration update completed!"
echo ""
echo "🚀 To test the new configuration:"
echo "   dotnet run --project src/Curio.Api"
echo ""
echo "📚 For more configuration options, see:"
echo "   docs/Quick-Setup-Guide.md"
