# Curio API - Development Environment Specific Settings
# This file contains development-specific overrides
# Safe to commit to version control

# Development-specific security settings (not for production!)
JWT_SECRET_KEY=development-jwt-secret-key-not-for-production-use-32-chars-minimum
ENCRYPTION_KEY=development-encryption-key-32-chars
ENCRYPTION_SALT=dev-salt-16-char

# Development database password (change in .env.local if needed)
DATABASE_PASSWORD=curio123

# Development logging levels
LOGGING_LEVEL_DEFAULT=Information
LOGGING_LEVEL_CURIO=Debug

# Development-specific features
ENABLE_SWAGGER=true
ENABLE_DETAILED_ERRORS=true
